import { system } from "@minecraft/server";
export const entitiesWithMusic = new Map([
    ["ditsh:ao_oni", "mob.ditsh.ao_oni.chase"],
    ["ditsh:armless", "mob.ditsh.armless.chase"],
    ["ditsh:headless_horseman", "mob.ditsh.headless_horseman.chase"],
    ["ditsh:herobrine", "mob.ditsh.herobrine.chase"],
    ["ditsh:jack_black", "mob.ditsh.jack_black.chase"],
    ["ditsh:jeff", "mob.ditsh.jeff.chase"],
    ["ditsh:mac_tonight", "mob.ditsh.mac_tonight.chase"],
    ["ditsh:mama_tattletail", "mob.ditsh.mama_tattletail.chase"],
    ["ditsh:murder_monkey", "mob.ditsh.murder_monkey.chase"],
    ["ditsh:mx", "mob.ditsh.mx.chase"],
    ["ditsh:nun", "mob.ditsh.nun.chase"],
    ["ditsh:rosemary", "mob.ditsh.rosemary.chase"],
    ["ditsh:scp096", "mob.ditsh.scp096.chase"]
]);
export const musicDurations = new Map([
    ["ditsh:ao_oni", 22.83],
    ["ditsh:armless", 22],
    ["ditsh:headless_horseman", 81],
    ["ditsh:herobrine", 24],
    ["ditsh:jack_black", 32],
    ["ditsh:jeff", 46],
    ["ditsh:mac_tonight", 30],
    ["ditsh:mama_tattletail", 39],
    ["ditsh:murder_monkey", 106],
    ["ditsh:mx", 10],
    ["ditsh:nun", 22],
    ["ditsh:rosemary", 89],
    ["ditsh:scp096", 14],
]);
const globalMusicTracker = new Map();
function isMusicExpired(entityTypeId) {
    const tracker = globalMusicTracker.get(entityTypeId);
    if (!tracker)
        return true;
    const duration = musicDurations.get(entityTypeId);
    if (!duration)
        return true;
    const currentTime = Date.now();
    const elapsedTime = (currentTime - tracker.startTime) / 1000;
    return elapsedTime >= duration;
}
export function cleanupExpiredMusic() {
    let cleanedCount = 0;
    for (const [entityTypeId] of globalMusicTracker) {
        if (isMusicExpired(entityTypeId)) {
            globalMusicTracker.delete(entityTypeId);
            cleanedCount++;
        }
    }
    if (cleanedCount > 0) {
        console.warn(`Cleaned up ${cleanedCount} expired music entries`);
    }
}
function canEntityPlayMusic(entity) {
    const tracker = globalMusicTracker.get(entity.typeId);
    if (!tracker)
        return true;
    if (isMusicExpired(entity.typeId)) {
        globalMusicTracker.delete(entity.typeId);
        return true;
    }
    if (tracker.entityId === entity.id)
        return true;
    return false;
}
export function playMusicForEntity(entity, music) {
    cleanupExpiredMusic();
    if (!canEntityPlayMusic(entity)) {
        console.warn(`Music blocked for ${entity.typeId} - another entity of same type is playing music`);
        return;
    }
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    let musicStarted = false;
    for (const player of nearbyPlayers) {
        let isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
        if (isCurrentlyPlaying && isMusicExpired(entity.typeId)) {
            player.setDynamicProperty(`${entity.typeId}_music`, false);
            isCurrentlyPlaying = false;
        }
        if (!isCurrentlyPlaying) {
            player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
            player.setDynamicProperty(`${entity.typeId}_music`, true);
            musicStarted = true;
        }
    }
    if (musicStarted) {
        globalMusicTracker.set(entity.typeId, {
            entityId: entity.id,
            startTime: Date.now()
        });
        console.warn(`Playing music for ${entity.typeId} (Entity ID: ${entity.id})`);
    }
    return;
}
export async function stopMusicForEntity(entity, music) {
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    for (const player of nearbyPlayers) {
        const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
        if (isCurrentlyPlaying) {
            player.runCommand(`stopsound @s ${music}`);
            await system.waitTicks(1);
            player.setDynamicProperty(`${entity.typeId}_music`, false);
        }
    }
    const tracker = globalMusicTracker.get(entity.typeId);
    if (tracker && tracker.entityId === entity.id) {
        globalMusicTracker.delete(entity.typeId);
        console.warn(`Stopped music for ${entity.typeId} (Entity ID: ${entity.id})`);
    }
    return;
}
export async function continueMusicForEntity(entity, music) {
    const playMusic = entity.getProperty("ditsh:playing_music");
    if (playMusic) {
        await system.waitTicks(140);
        cleanupExpiredMusic();
        if (!canEntityPlayMusic(entity)) {
            console.warn(`Music continuation blocked for ${entity.typeId} - another entity of same type is playing music`);
            return;
        }
        const nearbyPlayers = getNearbyPlayers(entity, 256);
        let musicStarted = false;
        for (const player of nearbyPlayers) {
            let isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
            if (isCurrentlyPlaying && isMusicExpired(entity.typeId)) {
                player.setDynamicProperty(`${entity.typeId}_music`, false);
                isCurrentlyPlaying = false;
            }
            if (!isCurrentlyPlaying) {
                player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
                player.setDynamicProperty(`${entity.typeId}_music`, true);
                musicStarted = true;
            }
        }
        if (musicStarted) {
            globalMusicTracker.set(entity.typeId, {
                entityId: entity.id,
                startTime: Date.now()
            });
            console.warn(`Continuing music for ${entity.typeId} (Entity ID: ${entity.id})`);
        }
    }
    return;
}
export function resetPlayerMusic(player) {
    for (const [entityTypeId, music] of entitiesWithMusic) {
        const isCurrentlyPlaying = isPlayerPlayingMusic(player, entityTypeId);
        if (isCurrentlyPlaying) {
            player.runCommand(`stopsound @s ${music}`);
            player.setDynamicProperty(`${entityTypeId}_music`, false);
        }
    }
    return;
}
export function getGlobalMusicStatus() {
    const entries = [];
    const currentTime = Date.now();
    for (const [entityTypeId, tracker] of globalMusicTracker) {
        const duration = musicDurations.get(entityTypeId) || 30;
        const elapsedTime = (currentTime - tracker.startTime) / 1000;
        const isExpired = elapsedTime >= duration;
        entries.push({
            entityTypeId,
            entityId: tracker.entityId,
            elapsedTime,
            duration,
            isExpired
        });
    }
    return {
        activeCount: globalMusicTracker.size,
        entries
    };
}
function getNearbyPlayers(entity, range) {
    const players = entity.dimension.getPlayers({ location: entity.location, maxDistance: range });
    return players;
}
function isPlayerPlayingMusic(player, entityTypeId) {
    const playingMusic = player.getDynamicProperty(`${entityTypeId}_music`);
    return playingMusic ?? false;
}
